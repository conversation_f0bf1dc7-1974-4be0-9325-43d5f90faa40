/* Custom Pagination Styling for Laravel Field Management System */

/* Fix oversized pagination controls */
.pagination {
    margin-bottom: 0;
    font-size: 0.875rem; /* 14px */
}

.pagination .page-link {
    padding: 0.375rem 0.75rem; /* Smaller padding for better sizing */
    font-size: 0.875rem; /* 14px */
    line-height: 1.5;
    border: 1px solid #dee2e6;
    color: #6c757d;
    background-color: #fff;
    border-radius: 0.375rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.pagination .page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination .page-link:focus {
    z-index: 3;
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.pagination .page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    pointer-events: none;
}

/* Specific styling for Previous/Next buttons */
.pagination .page-item:first-child .page-link,
.pagination .page-item:last-child .page-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* DataTables pagination wrapper */
.dataTables_wrapper .dataTables_paginate {
    margin: 0;
    text-align: end;
}

.dataTables_wrapper .dataTables_paginate .pagination {
    margin-bottom: 0;
    justify-content: end;
}

/* Ensure DataTables pagination matches Laravel pagination exactly */
.booking-pagination .dataTables_wrapper .dataTables_paginate .pagination {
    --bs-pagination-padding-x: 0.75rem;
    --bs-pagination-padding-y: 0.375rem;
    --bs-pagination-font-size: 0.875rem;
    --bs-pagination-color: #6c757d;
    --bs-pagination-bg: #fff;
    --bs-pagination-border-width: 1px;
    --bs-pagination-border-color: #dee2e6;
    --bs-pagination-border-radius: 0.375rem;
    --bs-pagination-hover-color: #0056b3;
    --bs-pagination-hover-bg: #e9ecef;
    --bs-pagination-hover-border-color: #dee2e6;
    --bs-pagination-focus-color: #0056b3;
    --bs-pagination-focus-bg: #e9ecef;
    --bs-pagination-focus-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    --bs-pagination-active-color: #fff;
    --bs-pagination-active-bg: #0d6efd;
    --bs-pagination-active-border-color: #0d6efd;
    --bs-pagination-disabled-color: #6c757d;
    --bs-pagination-disabled-bg: #fff;
    --bs-pagination-disabled-border-color: #dee2e6;
}

/* Ensure consistent button sizing for DataTables pagination */
.booking-pagination .dataTables_wrapper .dataTables_paginate .pagination .page-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border: 1px solid #dee2e6;
    color: #6c757d;
    background-color: #fff;
    border-radius: 0.375rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.booking-pagination .dataTables_wrapper .dataTables_paginate .pagination .page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.booking-pagination .dataTables_wrapper .dataTables_paginate .pagination .page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.booking-pagination .dataTables_wrapper .dataTables_paginate .pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    pointer-events: none;
}

/* Responsive pagination for smaller screens */
@media (max-width: 767.98px) {
    .pagination {
        font-size: 0.75rem; /* 12px on mobile */
        justify-content: center;
    }
    
    .pagination .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .dataTables_wrapper .dataTables_paginate .pagination {
        justify-content: center !important;
    }

    /* Ensure DataTables pagination buttons match Laravel pagination on mobile */
    .booking-pagination .dataTables_wrapper .dataTables_paginate .pagination .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 575.98px) {
    .pagination .page-link {
        padding: 0.25rem 0.4rem;
        font-size: 0.7rem;
    }

    /* Ensure DataTables pagination matches on very small screens */
    .booking-pagination .dataTables_wrapper .dataTables_paginate .pagination .page-link {
        padding: 0.25rem 0.4rem;
        font-size: 0.7rem;
    }
}

/* Ensure consistent spacing in pagination info */
.pagination-info {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0;
}

/* Fix for Laravel pagination links container */
.d-flex.justify-content-between.align-items-center {
    margin-top: 1rem;
}

.d-flex.justify-content-between.align-items-center .pagination {
    margin-bottom: 0;
}

/* Custom pagination style for admin tables */
.admin-pagination .pagination {
    --bs-pagination-padding-x: 0.75rem;
    --bs-pagination-padding-y: 0.375rem;
    --bs-pagination-font-size: 0.875rem;
    --bs-pagination-color: #6c757d;
    --bs-pagination-bg: #fff;
    --bs-pagination-border-width: 1px;
    --bs-pagination-border-color: #dee2e6;
    --bs-pagination-border-radius: 0.375rem;
    --bs-pagination-hover-color: #0056b3;
    --bs-pagination-hover-bg: #e9ecef;
    --bs-pagination-hover-border-color: #dee2e6;
    --bs-pagination-focus-color: #0056b3;
    --bs-pagination-focus-bg: #e9ecef;
    --bs-pagination-focus-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    --bs-pagination-active-color: #fff;
    --bs-pagination-active-bg: #0d6efd;
    --bs-pagination-active-border-color: #0d6efd;
    --bs-pagination-disabled-color: #6c757d;
    --bs-pagination-disabled-bg: #fff;
    --bs-pagination-disabled-border-color: #dee2e6;
}

/* Booking table specific pagination */
.booking-pagination {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.booking-pagination .pagination-info {
    font-size: 0.875rem;
    color: #6c757d;
}

.booking-pagination .pagination {
    margin-bottom: 0;
}
